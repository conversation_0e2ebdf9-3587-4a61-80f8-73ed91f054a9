# IQSF MVP - Global Guardian API

Real-time intelligence on LGBTQ+ safety and social risk, powered by the Logos AI Engine.

## Quick Start - Cloud Run Deployment

### Prerequisites
- Google Cloud SDK installed and authenticated
- Project ID: `iqsf-466106`
- Required environment variables (see `.env.example`)

### Deploy to Cloud Run
```bash
# Set your project
gcloud config set project iqsf-466106

# Deploy the service
./deploy.sh
```

### Environment Variables Required
- `PINECONE_API_KEY`: Your Pinecone API key
- `ADMIN_API_KEY`: Admin API key for protected endpoints

## API Endpoints

- `GET /`: Health check
- `GET /v1/qsi?location=<location>`: Get QSI analysis for a location
- `POST /v1/admin/ingest`: (Protected) Ingest new URLs into knowledge base

## Architecture

- **Backend**: FastAPI with AI models (SentenceTransformers, Transformers)
- **Vector DB**: Pinecone for semantic search
- **Deployment**: Google Cloud Run
- **Frontend**: React (separate deployment)
