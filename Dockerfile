# ==============================================================================
#                      IQSF API Dockerfile (V2 - Multi-Stage Build)
#   This file pre-downloads and caches the AI models into the final image
#   to ensure fast "cold starts" on serverless platforms like Cloud Run.
# ==============================================================================

# --- STAGE 1: The Builder ---
# This stage installs dependencies and downloads the models.
FROM python:3.10-slim AS builder

# Set a shared cache directory for transformers
ENV TRANSFORMERS_CACHE=/app/model_cache

WORKDIR /app

# Copy only the requirements file first to leverage Docker layer caching
COPY ./requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r /app/requirements.txt

# This is the magic step. We create a small Python script to download the models
# and then run it. This will save the models to our TRANSFORMERS_CACHE directory.
RUN python -c "from sentence_transformers import SentenceTransformer; from transformers import pipeline; SentenceTransformer('all-mpnet-base-v2'); pipeline('zero-shot-classification', model='typeform/distilbert-base-uncased-mnli')"


# --- STAGE 2: The Final Image ---
# This stage creates the lean, final image.
FROM python:3.10-slim

WORKDIR /app

# Copy the installed packages and the model cache from the "builder" stage
COPY --from=builder /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages
COPY --from=builder /app/model_cache /app/model_cache

# Set the environment variable so our application knows where to find the cached models
ENV TRANSFORMERS_CACHE=/app/model_cache

# Copy our application code
COPY ./api /app/api

# The command to run the Uvicorn server
CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8080"]
