# ==============================================================================
#                  IQSF - API DEPENDENCIES (V6 - Simplified for Development)
#        This version uses mock implementations when AI packages aren't available
#        to allow development with limited disk space.
# ==============================================================================

import os
from dotenv import load_dotenv

# Import required AI packages - REAL ONLY
import pinecone
from sentence_transformers import SentenceTransformer
from transformers import pipeline
import torch

# No mock classes - using real AI models only

# --- Load All Environment Variables from .env file ---
load_dotenv()
PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
ADMIN_API_KEY = os.getenv("ADMIN_API_KEY")

# --- Define Constants ---
# This is YOUR specific, unique host URL for the 768-dimension index.
PINECONE_INDEX_HOST = "https://iqsf-qsi-v1-1ckkiyl.svc.aped-4627-b74a.pinecone.io"

class EngineSingleton:
    """
    A singleton class to ensure our heavy AI models and database connections
    are initialized only ONCE when the API server starts up.
    """
    _instance = None

    def __init__(self):
        print("🚀 Initializing REAL AI Services for the API server...")

        # Initialize Pinecone - REAL ONLY
        print("   -> Connecting to Pinecone...")
        pc = pinecone.Pinecone(api_key=PINECONE_API_KEY)
        self.pinecone_index = pc.Index(host=PINECONE_INDEX_HOST)
        print(f"🌲 Pinecone connection established to host: {PINECONE_INDEX_HOST}")

        # Initialize embedding model - REAL ONLY
        print("   -> Loading embedding model (all-mpnet-base-v2)...")
        self.embedding_model = SentenceTransformer('all-mpnet-base-v2')
        print("   -> Embedding model loaded successfully")

        # Initialize classifier - REAL ONLY
        device = 0 if torch.cuda.is_available() else -1
        print(f"🧠 Loading models on {'GPU' if device == 0 else 'CPU'}...")
        print("   -> Loading zero-shot classifier (typeform/distilbert-base-uncased-mnli)...")
        self.classifier = pipeline(
            "zero-shot-classification",
            model="typeform/distilbert-base-uncased-mnli",
            device=device
        )
        print("   -> Classifier loaded successfully")

        print("✅ All REAL AI models loaded and ready.")

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

# --- Global Singleton Instance ---
engine_services = EngineSingleton.get_instance()


# --- Dependency Injection Functions ---
def get_pinecone_index():
    return engine_services.pinecone_index

def get_embedding_model():
    return engine_services.embedding_model

def get_classifier():
    return engine_services.classifier

def get_admin_api_key():
    if not ADMIN_API_KEY:
        raise ValueError("ADMIN_API_KEY not found in environment. Please set it in the .env file.")
    return ADMIN_API_KEY