# ==============================================================================
#                  IQSF - REFACTORED INGESTION SCRIPT (V5)
#      This version is lean and clean. It imports its targets from sources.py
#      and focuses solely on the ingestion process.
# ==============================================================================
print("DEBUG: Script execution started.")

import os
import hashlib
import time
from dotenv import load_dotenv
from pinecone import Pinecone
import requests
from unstructured.partition.html import partition_html
from unstructured.chunking.title import chunk_by_title
from sentence_transformers import SentenceTransformer

# Import the master list from our new catalog file.
from .sources import ALL_SOURCES_TO_INGEST

print("DEBUG: All imports successful.")

# --- Configuration & Setup ---
dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(dotenv_path=dotenv_path)

PINECONE_API_KEY = os.getenv("PINECONE_API_KEY")
PINECONE_INDEX_NAME = "iqsf-qsi-v1" # This must match your index name in Pinecone

if not PINECONE_API_KEY:
    raise ValueError("FATAL ERROR: PINECONE_API_KEY not set. Check .env file.")
print("DEBUG: Configuration loaded.")


def initialize_services():
    """Initializes Pinecone and the embedding model."""
    print("DEBUG: Initializing services...")
    try:
        pc = Pinecone(api_key=PINECONE_API_KEY)
        index = pc.Index(PINECONE_INDEX_NAME)
        model = SentenceTransformer('all-mpnet-base-v2')
        print("✅ DEBUG: Services Initialized (Pinecone & Embedding Model).")
        return index, model
    except Exception as e:
        raise ConnectionError(f"FATAL ERROR during service initialization: {e}")


def process_url(source_info: dict):
    """Fetches a URL, extracts text, and prepares document chunks."""
    url = source_info["url"]
    category = source_info.get("category", "general")
    
    print(f"DEBUG: Processing URL: {url}")
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=20)
        response.raise_for_status()
        
        elements = partition_html(text=response.text)
        chunks = chunk_by_title(elements, max_characters=1024, combine_under_n_chars=512)
        
        documents = []
        for chunk in chunks:
            text_chunk = chunk.text.strip()
            if len(text_chunk.split()) > 20:
                chunk_id = hashlib.md5((url + text_chunk).encode()).hexdigest()
                doc = { 
                    "id": chunk_id, 
                    "text": text_chunk, 
                    "metadata": { "source_url": url, "category": category } 
                }
                documents.append(doc)
        print(f"  -> DEBUG: Extracted {len(documents)} valid documents.")
        return documents
    except Exception as e:
        print(f"  -> ❌ DEBUG: FAILED to process URL. Error: {e}")
        return []


def main():
    """Main function to run the ingestion pipeline."""
    print("DEBUG: Ingestion process starting...")
    pinecone_index, embedding_model = initialize_services()
    
    all_processed_docs = []
    # The main loop is now cleaner and reads from the imported catalog.
    for source in ALL_SOURCES_TO_INGEST:
        processed_docs = process_url(source)
        all_processed_docs.extend(processed_docs)
        time.sleep(1) # Be a polite scraper

    if not all_processed_docs:
        print("DEBUG: No documents were successfully processed. Exiting.")
        return

    print(f"\nDEBUG: Embedding {len(all_processed_docs)} total document chunks...")
    texts_to_embed = [doc['text'] for doc in all_processed_docs]
    embeddings = embedding_model.encode(texts_to_embed, show_progress_bar=True).tolist()
    
    vectors_to_upsert = []
    for i, doc in enumerate(all_processed_docs):
        # We attach the generated embedding to the 'values' field for Pinecone
        vectors_to_upsert.append({
            "id": doc['id'],
            "values": embeddings[i],
            "metadata": {
                "text": doc['text'], # Keep the text in metadata for context
                "source_url": doc['metadata']['source_url'],
                "category": doc['metadata']['category']
            }
        })
    
    print(f"DEBUG: Preparing to upsert {len(vectors_to_upsert)} vectors to Pinecone...")
    
    # Upsert to Pinecone in batches for efficiency and reliability
    batch_size = 100
    for i in range(0, len(vectors_to_upsert), batch_size):
        batch = vectors_to_upsert[i:i + batch_size]
        print(f"DEBUG: Upserting batch {i//batch_s
ize + 1}...")
        try:
            pinecone_index.upsert(vectors=batch)
        except Exception as e:
            print(f"!!! FATAL ERROR during Pinecone upsert: {e}")
            # Decide if you want to exit or continue with other batches
            continue

    print("\n\n✅ DEBUG: Genesis Ingestion Sprint COMPLETE.")
    time.sleep(5) # Give Pinecone a moment to update stats
    final_stats = pinecone_index.describe_index_stats()
    print(f"DEBUG: Pinecone index final stats: {final_stats}")


if __name__ == "__main__":
    main()
    print("DEBUG: Script execution finished.")