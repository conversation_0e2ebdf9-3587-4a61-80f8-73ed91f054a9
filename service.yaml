apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: iqsf-api
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "5"
        autoscaling.knative.dev/minScale: "0"
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/execution-environment: gen2
    spec:
      containerConcurrency: 10
      timeoutSeconds: 900
      containers:
      - image: gcr.io/iqsf-466106/iqsf-api:latest
        ports:
        - containerPort: 8080
        env:
        - name: TRANSFORMERS_CACHE
          value: "/app/model_cache"
        - name: PINECONE_API_KEY
          valueFrom:
            secretKeyRef:
              name: iqsf-secrets
              key: PINECONE_API_KEY
        - name: ADMIN_API_KEY
          valueFrom:
            secretKeyRef:
              name: iqsf-secrets
              key: ADMIN_API_KEY
        resources:
          limits:
            cpu: "2000m"
            memory: "4Gi"
          requests:
            cpu: "1000m"
            memory: "2Gi"
        startupProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 60
          timeoutSeconds: 30
          periodSeconds: 10
          failureThreshold: 10
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 120
          timeoutSeconds: 30
          periodSeconds: 30
          failureThreshold: 3
