# IQSF MVP Cloud Run Deployment Guide

## 🚨 CRITICAL: Docker Reset Required

Your Docker Desktop has corrupted disk issues. You MUST reset it first:

### Option 1: Docker Desktop Reset
1. Open Docker Desktop
2. Go to Settings (⚙️) → Troubleshoot
3. Click "Reset to factory defaults"
4. Wait for reset to complete

### Option 2: Complete Reinstall (if reset fails)
1. Quit Docker Desktop
2. Drag Docker Desktop to Trash
3. Download fresh copy from docker.com
4. Install and start

## 📋 Pre-Deployment Setup

### 1. Create Environment File
```bash
cp .env.example .env
```

Edit `.env` with your actual values:
```
PINECONE_API_KEY=your_actual_pinecone_key
ADMIN_API_KEY=your_secure_admin_key
```

### 2. Verify Google Cloud Setup
```bash
# Check authentication
gcloud auth list

# Set project
gcloud config set project iqsf-466106

# Verify project
gcloud config get-value project
```

## 🚀 Deployment Options

### Option A: Automated Deployment
```bash
./deploy.sh
```

### Option B: Manual Deployment (Recommended after Docker reset)
```bash
./deploy-manual.sh
```

### Option C: Cloud Build (Alternative)
```bash
gcloud builds submit --config cloudbuild.yaml
```

## 🧪 Testing Your Deployment

After successful deployment, test these endpoints:

```bash
# Health check
curl https://your-service-url/

# QSI query
curl "https://your-service-url/v1/qsi?location=Texas,USA"

# API documentation
open https://your-service-url/docs
```

## 📊 Expected Resource Usage

- **Memory**: 4GB (for AI models)
- **CPU**: 2 vCPU
- **Cold Start**: ~60-120 seconds (models loading)
- **Warm Response**: <2 seconds

## 🔧 Troubleshooting

### Build Fails with I/O Errors
- Docker disk corruption - reset Docker Desktop

### Out of Memory Errors
- Increase memory allocation in deployment script

### Timeout During Startup
- Models are loading - wait up to 2 minutes

### Secret Access Errors
- Verify secrets exist: `gcloud secrets list`
- Check IAM permissions for Cloud Run service account

## 📝 Project Configuration

- **Project ID**: iqsf-466106
- **Region**: us-central1
- **Service Name**: iqsf-api
- **Repository**: iqsf-repo
- **Registry**: us-central1-docker.pkg.dev
