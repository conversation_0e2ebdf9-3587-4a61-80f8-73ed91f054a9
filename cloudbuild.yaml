steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/iqsf-repo/iqsf-api:$BUILD_ID',
      '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/iqsf-repo/iqsf-api:latest',
      '.'
    ]
    timeout: 1200s  # 20 minutes for AI model downloads

  # Push the container image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/iqsf-repo/iqsf-api:$BUILD_ID']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/iqsf-repo/iqsf-api:latest']

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args: [
      'run', 'deploy', 'iqsf-api',
      '--image', 'us-central1-docker.pkg.dev/$PROJECT_ID/iqsf-repo/iqsf-api:$BUILD_ID',
      '--region', 'us-central1',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '4Gi',
      '--cpu', '2',
      '--timeout', '900',
      '--concurrency', '10',
      '--max-instances', '5',
      '--set-env-vars', 'TRANSFORMERS_CACHE=/app/model_cache',
      '--set-secrets', 'PINECONE_API_KEY=PINECONE_API_KEY:latest,ADMIN_API_KEY=ADMIN_API_KEY:latest'
    ]

# Configure build options
options:
  machineType: 'E2_HIGHCPU_8'  # Use high-CPU machine for faster builds
  diskSizeGb: 100  # Larger disk for AI model downloads

# Build timeout
timeout: 1800s  # 30 minutes total timeout
