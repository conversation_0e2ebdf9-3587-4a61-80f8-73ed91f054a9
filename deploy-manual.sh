#!/bin/bash

# IQSF MVP Manual Deployment Script (Post Docker Reset)
# Use this script after resetting Docker Desktop

set -e

PROJECT_ID="iqsf-466106"
SERVICE_NAME="iqsf-api"
REGION="us-central1"
REPOSITORY="iqsf-repo"
IMAGE_NAME="us-central1-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY}/${SERVICE_NAME}"
VERSION="v1"

echo "🚀 IQSF MVP Manual Deployment (Post Docker Reset)"
echo "Project: ${PROJECT_ID}"
echo "Image: ${IMAGE_NAME}:${VERSION}"

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found. Create one based on .env.example"
    exit 1
fi

# Load environment variables
source .env

# Validate required variables
if [ -z "$PINECONE_API_KEY" ] || [ -z "$ADMIN_API_KEY" ]; then
    echo "❌ Error: Missing required environment variables"
    exit 1
fi

echo "✅ Environment variables loaded"

# Set project
gcloud config set project ${PROJECT_ID}

# Enable APIs
echo "🔧 Enabling APIs..."
gcloud services enable artifactregistry.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable secretmanager.googleapis.com

# Create repository
echo "📦 Creating Artifact Registry repository..."
gcloud artifacts repositories create ${REPOSITORY} \
    --repository-format=docker \
    --location=${REGION} \
    --description="IQSF MVP Docker images" || echo "Repository exists"

# Configure Docker auth
echo "🔐 Configuring Docker authentication..."
gcloud auth configure-docker ${REGION}-docker.pkg.dev

# Create secrets
echo "🔑 Creating secrets..."
echo -n "$PINECONE_API_KEY" | gcloud secrets create PINECONE_API_KEY --data-file=- --replication-policy=automatic || \
echo -n "$PINECONE_API_KEY" | gcloud secrets versions add PINECONE_API_KEY --data-file=-

echo -n "$ADMIN_API_KEY" | gcloud secrets create ADMIN_API_KEY --data-file=- --replication-policy=automatic || \
echo -n "$ADMIN_API_KEY" | gcloud secrets versions add ADMIN_API_KEY --data-file=-

# Build image
echo "🏗️  Building Docker image..."
docker build -t ${IMAGE_NAME}:${VERSION} .

# Push image
echo "📤 Pushing to Artifact Registry..."
docker push ${IMAGE_NAME}:${VERSION}

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME}:${VERSION} \
    --region ${REGION} \
    --platform managed \
    --allow-unauthenticated \
    --memory 4Gi \
    --cpu 2 \
    --timeout 900 \
    --concurrency 10 \
    --max-instances 5 \
    --set-env-vars TRANSFORMERS_CACHE=/app/model_cache \
    --set-secrets PINECONE_API_KEY=PINECONE_API_KEY:latest,ADMIN_API_KEY=ADMIN_API_KEY:latest

# Get service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)')

echo ""
echo "✅ Deployment successful!"
echo "🌐 Service URL: ${SERVICE_URL}"
echo ""
echo "Test commands:"
echo "curl ${SERVICE_URL}/"
echo "curl \"${SERVICE_URL}/v1/qsi?location=Texas,USA\""
echo ""
echo "📚 API Docs: ${SERVICE_URL}/docs"
